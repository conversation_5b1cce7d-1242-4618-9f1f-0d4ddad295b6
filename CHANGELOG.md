# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]
### Added
- Highlight which time sheet has an error when saving
- Sentry error reporting

### Changed
- Pin button layout now follows mobile conventions
- Schedule week view now starts on monday

### Fixed
- Logout not returning to login screen
- Submitted tasks now have the correct subject id
- Submitted tasks now have the correct title

## [1.1.0]
### Add
- Analytics gathering

### Changed
- Improved performance of edit hour page 
- New daily schedule UI
- New hour registration screen
- Show lock when editing hours is prohibited
- Style changes on availability page
- Style changes on absence page
- Style changes on hour page 
- Style changes on settings page
- Turn on analytics by default
- Use ionic animations
- Upgrade to Angular 8
- Upgrade to Ionic 5

### Fixed
- Break settings not being applied when updating timesheet
- Date selector translation on edit hour page
- Incorrect error when editing hours
- White page when navigating back from edit hour page to daily hour page

### Removed
- Animate.css dependency
