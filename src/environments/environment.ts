// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
    production: false,
    API_BASE_URL: '',
    API_WEATHER_BASE: 'https://meteoserver.nl/api/liveweer.php',
    API_WEATHER_KEY: '54d334e31a',
    STORAGE_ENCRYPTION_KEY: 'SuperSecureTestingKey23456789',
    CACHE_TIME: 0, // don't cache when testing,
    TRELLO_API_KEY: '********************************',
    TRELLO_API_TOKEN: 'a35870bc8f3bb8aa69aab48de4aff5e792618c6c798566b9aab75584fa4410c3',
    TRELLO_URL: 'https://api.trello.com/1',
    useDynamicStyling: false,
    USE_CACHE: false,
    TASK: {
        SUBJECT_ID: '93eba5dc-7b65-4cc2-a274-c43ed45343e1',
        TASK_TYPE: '94084a33-d2df-47cc-a523-a4a1cc827f05',
    },
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
