// http://ionicframework.com/docs/theming/
@import '~@ionic/angular/css/core.css';
@import '~@ionic/angular/css/normalize.css';
@import '~@ionic/angular/css/structure.css';
@import '~@ionic/angular/css/typography.css';
@import '~@ionic/angular/css/padding.css';
@import '~@ionic/angular/css/float-elements.css';
@import '~@ionic/angular/css/text-alignment.css';
@import '~@ionic/angular/css/text-transformation.css';
@import '~@ionic/angular/css/flex-utils.css';

.header-image {
  border-bottom: solid 5px var(--ion-color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  height: 15vh;

  img-loader {
    height: 100%;

    img {
      height: 100%;
    }
  }
}

.ios {

  .sa-menu-header {
    padding-top: 48px !important;
  }
}

.sa-gradient-primary {
  background-image: radial-gradient(circle at 50% 40%, rgba(var(--ion-color-primary-rgb), 0.8), rgba(var(--ion-color-primary-rgb), 1) 50%);
}

.sa-button-shadow {
  --background: rgba(0, 0, 0, .4);
  --color: white;
}

.sa-content {
  height: 100%;
  width: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.sa-color-light {
  color: var(--ion-color-light);
}

.sa-no-padding-left {
  padding-left: 0;
}

.sa-no-padding-right {
  padding-right: 0;
}

ion-content {
  --background: var(--ion-item-background);
}

.large-divider {
  border: none;
  height: 46px;
  font-size: 16px;
}

.dashboard-card {
  height: 110px;
  margin: 0;
  padding: 10px;
  align-self: center;
  justify-self: center;

  color: var(--ion-color-font);
  background-color: var(--background);

  ion-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 2.5rem;
  }

  .sa-dashboard-loader {
    margin-top: 10%;
    height: 30px;
  }

  .sa-dashboard-header-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: flex-start;
    padding-bottom: 20px;
  }
}

.news-card {
  align-self: start;
  height: auto;
  margin-bottom: 10px;

  ion-card-content {
    align-items: start;
    padding: 0;

    ion-label {
      font-size: 1rem;
    }

    .no-news {
      font-size: 1.5rem;
      padding: 10px 0;
    }
  }
}

.sa-no-profile-image {
  background-color: var(--ion-color-primary);
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  border-radius: 25px;
}

ion-modal.date-modal {
  min-height: 200px;
  --width: 290px;
  --height: auto;
  --border-radius: 8px;
  
  .ion-page {
      position: relative;
      display: block;
      contain: content;

      .inner-content {
          max-height: 80vh;
          overflow: auto;
      }
  }
}
