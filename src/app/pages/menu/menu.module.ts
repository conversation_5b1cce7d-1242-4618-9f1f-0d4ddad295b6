import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {RouterModule, Routes} from '@angular/router';

import {MenuPage} from './menu.page';
import {MenuModule} from '@staffsupport/menu';


const routes: Routes = [
    {
        path: '',
        component: MenuPage,
        children: [
            {
                path: 'tabs',
                loadChildren: () => import('../tabs/tabs.module').then(m => m.TabsPageModule)
            },
            {
                path: 'about',
                loadChildren: () => import('../about/about.module').then(m => m.AboutPageModule)
            },
        ]
    }
];

@NgModule({
    imports: [
        CommonModule,
        MenuModule.forRoot({
            pageConfigurationUrl: 'assets/appPages.json',
            headerImageUrl: 'assets/logo/staff.png'
        }),
        RouterModule.forChild(routes)
    ],
    declarations: [
        MenuPage
    ]
})
export class MenuPageModule {
}
