<ion-tabs>
    <ion-tab-bar slot="bottom">
        <ion-tab-button tab="home">
            <ion-icon name="home"></ion-icon>
        </ion-tab-button>
        <ion-tab-button tab="availability">
            <ion-icon name="checkmark-circle"></ion-icon>
        </ion-tab-button>
        <ion-tab-button tab="schedule">
            <ion-icon name="calendar"></ion-icon>
        </ion-tab-button>
        <ion-tab-button tab="hours">
            <ion-icon name="time"></ion-icon>
        </ion-tab-button>
        <ion-tab-button tab="news" (click)="scrollNewsToTop()">
            <ion-icon name="newspaper">
            </ion-icon>
        </ion-tab-button>
        <ion-tab-button (click)="openMenu()">
            <ion-icon name="menu"></ion-icon>
            <ion-badge *ngIf="notificationsAmount > 0" id="notifications-badge"
                       color="danger">{{ notificationsAmount }}</ion-badge>
        </ion-tab-button>
    </ion-tab-bar>
</ion-tabs>
