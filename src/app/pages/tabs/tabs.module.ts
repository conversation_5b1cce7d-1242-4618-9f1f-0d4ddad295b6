import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {RouterModule, Routes} from '@angular/router';

import {IonicModule} from '@ionic/angular';

import {TabsPage} from './tabs.page';
import {AppFunctionGuard, AppFunctionRights} from '@staffsupport/common';

const routes: Routes = [
    {
        path: '',
        component: TabsPage,
        children: [
            {
                path: 'home',
                loadChildren: () => import('../home/<USER>').then(m => m.HomePageModule)
            },
            {
                path: 'schedule',
                loadChildren: () => import('../schedule/schedule.module').then(m => m.SchedulePageModule),
                canActivate: [AppFunctionGuard],
                data: {
                    appFunction: AppFunctionRights.Schedule
                }
            },
            {
                path: 'profile',
                loadChildren: () => import('../profile/profile.module').then(m => m.ProfilePageModule),
                canActivate: [AppFunctionGuard],
                data: {
                    appFunction: AppFunctionRights.Profile
                }
            },
            {
                path: 'availability',
                loadChildren: () => import('../availability/availability.module').then(m => m.AvailabilityPageModule),
                canActivate: [AppFunctionGuard],
                data: {
                    appFunction: AppFunctionRights.Availability
                }
            },
            {
                path: 'notifications',
                loadChildren: () => import('../notifications/notifications.module').then(m => m.NotificationsPageModule),
            },
            {
                path: 'documents',
                loadChildren: () => import('../documents/documents.module').then(m => m.DocumentsPageModule),
            },
            {
                path: 'absence',
                loadChildren: () => import('../absence/absence.module').then(m => m.AbsencePageModule),
            },
            {
                path: 'news',
                loadChildren: () => import('../news/news.module').then(m => m.NewsPageModule),
            },
            {
                path: 'settings',
                loadChildren: () => import('../settings/settings.module').then(m => m.SettingsPageModule),
            },
            {
                path: 'hours',
                loadChildren: () => import('../hours/hours.module').then(m => m.HoursPageModule),
                canActivate: [AppFunctionGuard],
                data: {
                    appFunction: AppFunctionRights.Hours
                }
            },
            {
                path: 'clocking',
                loadChildren: () => import('../clocking/clocking.module').then(m => m.ClockingPageModule),
                canActivate: [AppFunctionGuard],
                data: {
                    appFunction: AppFunctionRights.Clocking
                }
            },
        ],
    }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        RouterModule.forChild(routes)
    ],
    declarations: [
        TabsPage,
    ],
    providers: [
        AppFunctionGuard
    ]
})
export class TabsPageModule {
}
