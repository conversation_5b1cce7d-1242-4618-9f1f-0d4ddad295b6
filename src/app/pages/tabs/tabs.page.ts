import {Component, OnInit} from '@angular/core';
import {MenuController, ModalController} from '@ionic/angular';
import {Router} from '@angular/router';
import {Plugins} from '@capacitor/core';
import {EventService} from '@staffsupport/common';
import {NotificationsService} from '@staffsupport/notifications';

const {App} = Plugins;

@Component({
    selector: 'app-tabs',
    templateUrl: './tabs.page.html',
    styleUrls: ['./tabs.page.scss'],
})
export class TabsPage implements OnInit {
    notificationsAmount: number;

    constructor(private menu: MenuController,
                private notificationService: NotificationsService,
                private modalController: ModalController,
                private router: Router,
                private events: EventService) {
    }

    ngOnInit() {
        App.addListener('backButton', (event) => {
            if (this.router.isActive('/menu/tabs/home', true)) {
                App.exitApp();
            }
        });

        this.notificationService.notifications.subscribe((notifications) => {
            this.notificationsAmount = notifications.length;
        });
    }

    openMenu() {
        this.menu.toggle();
    }

    scrollNewsToTop() {
        const isActive = this.router.isActive('/menu/tabs/news', true);

        if (isActive) {
            this.events.emit('news.scroll');
        }
    }
}
