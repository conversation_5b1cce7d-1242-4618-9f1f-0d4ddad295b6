import { Component } from '@angular/core';

import { App, AppState } from '@capacitor/app';
import { StatusBar } from '@capacitor/status-bar';
import { SplashScreen } from '@capacitor/splash-screen';
import { MenuController, ModalController, Platform } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AuthenticationService, StorageService } from '@staffsupport/common';
import { Router } from '@angular/router';
import { CacheService } from 'ionic-cache';
import { NotificationsService } from '@staffsupport/notifications';
import { NewsModalComponent, NewsService } from '@staffsupport/news';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
})
export class AppComponent {
  constructor(
    private platform: Platform,
    private translate: TranslateService,
    private authService: AuthenticationService,
    private router: Router,
    private notificationService: NotificationsService,
    private menu: MenuController,
    private modalController: ModalController,
    private newsService: NewsService,
    private storage: StorageService
  ) {
    this.init();
  }

  async init() {
    // Translations are stored in: assets/i18n/[language code].json
    this.translate.addLangs(['en', 'nl']);

    this.storage.get(this.storage.keys.SETTINGS.LANGUAGE).then((lang) => {
      // Default to dutch
      if (!lang) {
        this.storage.set(this.storage.keys.SETTINGS.LANGUAGE, 'nl');
        lang = 'nl';
      }

      this.translate.use(lang);
    });

    const useDarkMode = JSON.parse(
      await this.storage.get(this.storage.keys.SETTINGS.DARKMODE)
    );
    if (useDarkMode) {
      const body = document.getElementsByTagName('body')[0];
      body.classList.add('dark');
    }

    this.loadNotifications();
    this.loadPopupNews();

    App.addListener('appStateChange', (state: AppState) => {
      if (state.isActive) {
        this.loadPopupNews();
        this.loadNotifications();
      }
    });

    this.initializeApp();
  }

  initializeApp() {
    this.platform.ready().then(() => {
      StatusBar.show();
      SplashScreen.hide();
    });
  }

  logout() {
    this.authService.logout().then(() => {
      this.menu.close();
      this.router.navigateByUrl('/');
    });
  }

  openMenu() {
    this.menu.toggle('side-menu');
  }

  loadNotifications() {
    this.authService.isAuthenticated().then((isAuthenticated) => {
      if (isAuthenticated) {
        this.notificationService.getUnread().subscribe((notifications) => {
          this.notificationService.updateNotifications(notifications);
        });
      }
    });
  }

  loadPopupNews() {
    this.authService.isAuthenticated().then((isAuthenticated) => {
      if (isAuthenticated) {
        this.newsService.getPopUpNews().subscribe(async (news) => {
          if (news.length > 0) {
            const modal = await this.modalController.create({
              component: NewsModalComponent,
              componentProps: {
                newsItems: news,
              },
            });
            modal.present();
          }
        });
      }
    });
  }
}
