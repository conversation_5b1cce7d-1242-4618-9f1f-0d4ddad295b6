import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {FirstTimeGuard} from '@staffsupport/common';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'scanner',
        pathMatch: 'full'
    },
    {
        path: 'menu',
        loadChildren: () => import('./pages/menu/menu.module').then(m => m.MenuPageModule)
    },
    {
        path: 'pin',
        loadChildren: () => import('./pages/pin/pin.module').then(m => m.PinPageModule)
    },
    {
        path: 'login',
        loadChildren: () => import('./pages/login/login.module').then(m => m.LoginPageModule)
    },
    {
        path: 'scanner',
        loadChildren: () => import('./pages/scanner/scanner.module').then(m => m.ScannerPageModule),
        canActivate: [FirstTimeGuard]
    },

];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule]
})
export class AppRoutingModule {
}
