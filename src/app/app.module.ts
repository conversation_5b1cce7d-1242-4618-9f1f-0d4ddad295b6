// Angular
import {APP_INITIALIZER, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';
import {RouteReuseStrategy} from '@angular/router';
import {HTTP_INTERCEPTORS, HttpClient, HttpClientModule} from '@angular/common/http';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {registerLocaleData} from '@angular/common';
import localNl from '@angular/common/locales/nl';
import localEn from '@angular/common/locales/en';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
// Ionic
import {IonicModule, IonicRouteStrategy} from '@ionic/angular';
import {FileOpener} from '@ionic-native/file-opener/ngx';
import {CacheModule, CacheService} from 'ionic-cache';
import {IonicSelectableModule} from 'ionic-selectable';
import {CalendarModule} from 'ion2-calendar';
import {Storage} from '@ionic/storage';
// App services
import {AppComponent} from './app.component';
import {AppRoutingModule} from './app-routing.module';
import {environment} from '../environments/environment';
import {NgxMaterialTimepickerModule} from 'ngx-material-timepicker';
import * as Sentry from '@sentry/browser';
import {
    AuthenticationService,
    AuthInterceptor,
    CommonModule,
    ExceptionInterceptor,
    FirstTimeGuard,
    BaseUrlInterceptor,
    SentryErrorHandler,
    StorageService,
    ThemeService,
    UsePinGuard
} from '@staffsupport/common';
import {BehaviorSubject} from 'rxjs';
import {AvailabilityService} from '@staffsupport/availability';
import {AbsenceService} from '@staffsupport/absence';
import {ClockingService} from '@staffsupport/clocking';
import {HoursService} from '@staffsupport/hours';
import {NewsModule, NewsService} from '@staffsupport/news';
import {NotificationsService} from '@staffsupport/notifications';
import {ScheduleService} from '@staffsupport/schedule';

Sentry.init({
    dsn: 'https://<EMAIL>/5286676',
    // TryCatch has to be configured to disable XMLHttpRequest wrapping, as we are going to handle
    // http module exceptions manually in Angular's ErrorHandler and we don't want it to capture the same error twice.
    // Please note that TryCatch configuration requires at least @sentry/browser v5.16.0.
    integrations: [new Sentry.Integrations.TryCatch({
        XMLHttpRequest: false,
    })],
});

@NgModule({
    declarations: [
        AppComponent,
    ],
    imports: [
        BrowserModule,
        CommonModule.forRoot({
            encryptionKey: environment.STORAGE_ENCRYPTION_KEY,
            isProduction: environment.production
        }),
        BrowserAnimationsModule,
        CacheModule.forRoot({keyPrefix: 'staff-medewerker-cache'}),
        IonicModule.forRoot({
            backButtonText: ''
        }),
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: (createTranslateLoader),
                deps: [HttpClient]
            }
        }),
        AppRoutingModule,
        HttpClientModule,
        IonicSelectableModule,
        CalendarModule,
        FormsModule,
        ReactiveFormsModule,
        NgxMaterialTimepickerModule,
        NewsModule
    ],
    providers: [
        {provide: ErrorHandler, useClass: SentryErrorHandler},
        {provide: RouteReuseStrategy, useClass: IonicRouteStrategy},
        {provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true},
        {provide: HTTP_INTERCEPTORS, useClass: ExceptionInterceptor, multi: true},
        {provide: HTTP_INTERCEPTORS, useClass: BaseUrlInterceptor, multi: true},
        {provide: 'baseUrl', useValue: new BehaviorSubject('')},
        FirstTimeGuard,
        UsePinGuard,
        AbsenceService,
        AvailabilityService,
        HoursService,
        NewsService,
        NotificationsService,
        ScheduleService,
        ClockingService,
        {
            provide: APP_INITIALIZER,
            useFactory: cacheFactory,
            deps: [
                CacheService, Storage
            ],
            multi: true
        },
        {
            provide: APP_INITIALIZER,
            useFactory: apiBaseFactory,
            deps: [StorageService, 'baseUrl'],
            multi: true
        },
        {
            provide: APP_INITIALIZER,
            useFactory: loadStyle,
            deps: [ThemeService, AuthenticationService],
            multi: true
        },
        {
            provide: APP_INITIALIZER,
            useFactory: setEnteredPinFalse,
            deps: [AuthenticationService],
            multi: true
        },
        {
            provide: APP_INITIALIZER,
            useFactory: updateUserInfo,
            deps: [AuthenticationService],
            multi: true
        },
        FileOpener
    ],
    exports: [],
    bootstrap: [
        AppComponent
    ]
})
export class AppModule {
}

registerLocaleData(localEn, 'en');
registerLocaleData(localNl, 'nl');

export function loadStyle(service: ThemeService) {
    if (environment.useDynamicStyling) {
        return () => service.getAndLoadStyle();
    } else {
        return () => {
        };
    }
}

export function createTranslateLoader(http: HttpClient) {
    return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

export function setEnteredPinFalse(authenticationService: AuthenticationService) {
    return () => authenticationService.setPinEntered('false');
}

export function updateUserInfo(authenticationService: AuthenticationService) {
    return () => {
        authenticationService.isAuthenticated().then((result) => {
            if (result) {
                authenticationService.getUserInfo().subscribe(async (user) => {
                    if (user) {
                        await authenticationService.setUser(user);
                    }
                });
            }
        });
    };
}

export function cacheFactory(cacheService: CacheService, storage: Storage) {
    return async () => {
        await storage.ready();
        cacheService.setDefaultTTL(environment.CACHE_TIME);
        cacheService.enableCache(environment.USE_CACHE);
    };
}

export function apiBaseFactory(storageService: StorageService, baseUrl: BehaviorSubject<string>) {
    return () => {
        storageService.get(storageService.API_BASE).then((base) => {
            baseUrl.next(base);
        });
    };
}

