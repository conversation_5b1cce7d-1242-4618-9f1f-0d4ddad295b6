{"name": "staff-app", "version": "1.0.4", "author": "Staff Support B.V.", "homepage": "http://www.staff.nl/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "postinstall": "jetifier"}, "private": true, "dependencies": {"@angular/animations": "12.2.16", "@angular/common": "12.2.16", "@angular/core": "12.2.16", "@angular/forms": "12.2.16", "@angular/platform-browser": "12.2.16", "@angular/platform-browser-dynamic": "12.2.16", "@angular/router": "12.2.16", "@capacitor-community/firebase-analytics": "1.0.1", "@capacitor/android": "^5.0.0", "@capacitor/app": "^5.0.0", "@capacitor/browser": "^5.0.0", "@capacitor/camera": "^5.0.0", "@capacitor/core": "^5.0.0", "@capacitor/device": "^5.0.0", "@capacitor/filesystem": "^5.0.0", "@capacitor/haptics": "^5.0.0", "@capacitor/ios": "^5.0.0", "@capacitor/keyboard": "^5.0.0", "@capacitor/splash-screen": "^5.0.0", "@capacitor/status-bar": "^5.0.0", "@capacitor/storage": "1.2.3", "@fullcalendar/angular": "5.10.2", "@fullcalendar/core": "5.10.1", "@fullcalendar/daygrid": "5.10.1", "@fullcalendar/list": "5.10.1", "@fullcalendar/moment": "5.10.1", "@ionic-native/barcode-scanner": "^5.36.0", "@ionic-native/core": "^5.36.0", "@ionic-native/file-opener": "^5.36.0", "@ionic/angular": "^6.1.7", "@ionic/pwa-elements": "^1.3.0", "@ionic/storage": "2.3.1", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@sentry/browser": "^5.18.0", "@staffsupport/about": "3.0.1", "@staffsupport/absence": "3.0.2", "@staffsupport/availability": "3.0.2", "@staffsupport/clocking": "3.0.2", "@staffsupport/common": "3.0.5", "@staffsupport/dashboard": "3.0.2", "@staffsupport/documents": "3.0.1", "@staffsupport/hours": "3.0.2", "@staffsupport/login": "3.0.4", "@staffsupport/menu": "3.0.1", "@staffsupport/news": "3.0.1", "@staffsupport/notifications": "3.0.2", "@staffsupport/pin": "3.0.1", "@staffsupport/profile": "3.0.3", "@staffsupport/register": "3.0.1", "@staffsupport/schedule": "3.1.6", "@staffsupport/settings": "3.0.2", "cordova-plugin-file": "^6.0.2", "cordova-plugin-file-opener2": "^2.2.1", "core-js": "^2.6.9", "crypto-js": "^3.1.9-1", "ionic-cache": "5.2.0", "ionic-selectable": "4.9.0", "jetifier": "^2.0.0", "moment": "^2.24.0", "ngx-material-timepicker": "^5.5.1", "phonegap-plugin-barcodescanner": "^8.1.0", "rxjs": "^6.6.7", "tslib": "^2.0.0", "url-parse": "^1.4.7", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.2.16", "@angular-devkit/core": "~12.2.16", "@angular-devkit/schematics": "~12.2.16", "@angular/cli": "~12.2.16", "@angular/compiler-cli": "~12.2.16", "@angular/language-service": "~12.2.16", "@capacitor/cli": "^5.0.0", "@ionic/angular-toolkit": "6.1.0", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/node": "~8.9.4", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.20", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.5.0", "ng-packagr": "^12.2.7", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~4.3.5"}, "description": "Staff Medewerker", "cordova": {"plugins": {"cordova-plugin-file": {}, "phonegap-plugin-barcodescanner": {}, "cordova-plugin-file-opener2": {}}, "platforms": ["android", "browser"]}}