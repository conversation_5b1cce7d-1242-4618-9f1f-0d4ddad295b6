version: 2
jobs:
  build:
    docker:
      - image: circleci/android:api-28-node
    working_directory: ~/code
    environment:
      JVM_OPTS: -Xmx3200m
    steps:
      - checkout
      - run:
          name: Authenticate with registry
          command: echo "//registry.npmjs.org/:_authToken=$npm_TOKEN" > ~/code/.npmrc
      - run:
          name: "Install ionic and capacitor"
          command: |
            sudo npm install -g ionic capacitor
      - run:
          name: "Install npm packages"
          command: |
            npm install
      - run:
          name: "Install Capacitor plugins and add android platform"
          command: |
            ionic build --prod
