# Specifies intentionally untracked files to ignore when using Git
# http://git-scm.com/docs/gitignore

*~
*.sw[mnpcod]
*.log
*.lock
*.tmp
*.tmp.*
log.txt
*.sublime-project
*.sublime-workspace

.idea/
.vscode/
.sass-cache/
.versions/
coverage/
collection/
resources/
plugins/
dist/
node_modules/
tmp/
temp/
core/theme-builder/
core/test-components/
core/css/
angular/css/
$RECYCLE.BIN/
www/

.DS_Store
Thumbs.db
UserInterfaceState.xcuserstate
.env

.package.tmp.json

src/themes/version.scss
scripts/e2e/webpackEntryPoints.json
scripts/build/e2e-generated-tsconfig.json
*.css.ts

stats.json

# stencil
angular/css/
core/css/
core/loader/
.stencil/
angular/build/
