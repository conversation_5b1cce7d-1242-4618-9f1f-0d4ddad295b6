# Staff medewerker app
This project contains a [Ionic](https://ionicframework.com/) hybrid app which is written using [Angular](https://angular.io/) (HTML CSS, Typscript) and can be build to native platforms like Android or IOS.

## Setting up dev environment
In order to develop and build the app a few dependencies are required.

1. Install the current Node LTS version from the [node website](https://nodejs.org/en/download/)
2. Open a shell and Install Ionic on your machine using the following command:
`npm install -g ionic`

## Running the app
1. clone the repo and enter the cloned directory with a shell
2. run the `npm install` command, this will download all the project dependencies (This will take some time)
3. run the `ionic serve` command. This will run the app in dev mode and open a browser
4. Open the browser devtools (F12 in Chrome) and hit the 'Toggle device toolbar button' (Ctrl + shift + m). This will switch to a mobile view port 

## Making changes
Once the app is served changes made in the code will prompt an automatic recompile and changes will be directly visible in the browser. Try changing a title string for easy oberving.
